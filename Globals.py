
# 🍎 优化的知识蒸馏配置 - 回到成功的fruit数据集
model_file = "ultralytics/cfg/models/v8/yolov8-lite-fruit.yaml"  # 使用专门的水果检测架构
dataset = "ultralytics/cfg/datasets/fruit.yaml"  # 回到原始高质量fruit数据集
epochs = 150  # 增加训练轮数以充分利用知识蒸馏
bool_save = True
save_period = 25  # 调整保存频率
bool_resume = False
device = '0'
batch_size = 16  # 增加batch size提升训练效率
test_graph = "./(f)images/orange.jpg"
scale_default = 'n'
exchange = 1

# 启用蒸馏 - 使用成功的配置
bool_distill = True
# 恢复成功的层配置 - 与yolov8-lite-fruit相同的模式
teacher_peer_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 45]
student_peer_list = [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46]
flag_train_output = 0  # 启用蒸馏模式
bool_val_output = True
scale_student = 'n'
scale_teacher = 's'

# 教师模型配置
path_teacher = "yolov8s.pt"
path_teacher_prepare = "./(f)models/yolov8_teacher_prepare-fruit.pth"
path_student = "yolov8n.pt"

# 🔥 优化蒸馏参数 - 基于用户记忆的最佳实践
hyp_T = 4.0  # 适中的温度参数，平衡软化和信息保留
hyp_box_distill = 0.7  # 增强bbox蒸馏权重
hyp_cls_distill = 0.9  # 增强分类蒸馏权重
hyp_dfl_distill = 0.8  # 增强DFL蒸馏权重
hyp_w_t_cls = 0.85  # 85%教师指导 + 15%GT，偏向教师指导
hyp_w_t_box = 0.80  # 80%教师指导 + 20%GT
hyp_w_t_dfl = 0.82  # 82%教师指导 + 18%GT

# 🚀 增强训练参数
label_smoothing = 0.15  # 增加标签平滑，减少过拟合

# 文件路径配置
model_based_file = "./(f)models/model_based_file.pt"
path_student_prepare = './(f)models/yolov8_student_prepare-fruit.pth'
path_teacher_prepare = './(f)models/yolov8_teacher_prepare-fruit.pth'
final = './(f)models/yolov8_lite_fruit_enhanced.pt'
phase = 1

# 🎯 优化策略配置 - 启用高级特性
use_feature_layer_stats = True  # 启用特征层统计
use_dynamic_temperature = True  # 启用动态温度调整
use_hard_negative_mining = True  # 启用困难负样本挖掘
use_contrastive_learning = True  # 启用对比学习
margin_constraint_threshold = 0.6  # 调整边界约束阈值

# 训练监控配置
monitor_loss_trends = True
monitor_memory_usage = True
compare_with_baseline = True

# 其他参数
hyp_lr0 = 0.01
hyp_lrf = 0.01
hyp_momentum = 0.937
hyp_weight_decay = 0.0005
hyp_warmup_epochs = 3.0
hyp_warmup_momentum = 0.8
hyp_warmup_bias_lr = 0.1
hyp_box = 7.5
hyp_cls = 0.5
hyp_dfl = 1.5
hyp_pose = 12.0
hyp_kobj = 1.0
hyp_label_smoothing = 0.0
hyp_nbs = 64
hyp_overlap_mask = True
hyp_mask_ratio = 4
hyp_dropout = 0.0
hyp_val = True
