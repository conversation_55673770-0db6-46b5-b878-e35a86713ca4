#!/usr/bin/env python3
"""
COCO知识蒸馏训练 - 基于成功的水果数据集配置
"""

import torch
from ultralytics import YOLO
from Globals import *

def load_pretrained_weights_intelligently(model):
    """
    智能加载预训练权重到双头知识蒸馏模型
    为教师和学生网络分别加载兼容的YOLOv8权重
    """
    print("🔧 开始智能权重加载...")

    try:
        # 加载标准YOLOv8n预训练权重
        pretrained_model = YOLO('yolov8n.pt')
        pretrained_state = pretrained_model.model.state_dict()
        current_state = model.model.state_dict()

        loaded_count = 0
        skipped_count = 0

        # 定义教师和学生网络的层映射
        teacher_layers = list(range(1, 23))  # 教师网络层 1-22
        student_layers = list(range(23, 45))  # 学生网络层 23-44

        print("📋 权重映射策略:")
        print(f"   教师网络层: {teacher_layers}")
        print(f"   学生网络层: {student_layers}")

        # 为教师网络加载权重
        for teacher_idx, pretrained_idx in enumerate(teacher_layers):
            teacher_layer_name = f"model.{pretrained_idx}."
            pretrained_layer_name = f"model.{teacher_idx + 1}."

            for key in pretrained_state.keys():
                if key.startswith(pretrained_layer_name):
                    new_key = key.replace(pretrained_layer_name, teacher_layer_name)
                    if new_key in current_state and current_state[new_key].shape == pretrained_state[key].shape:
                        current_state[new_key] = pretrained_state[key].clone()
                        loaded_count += 1
                    else:
                        skipped_count += 1

        # 为学生网络加载权重（复用教师权重）
        for student_idx, teacher_idx in enumerate(student_layers):
            student_layer_name = f"model.{teacher_idx}."
            teacher_layer_name = f"model.{student_idx + 1}."

            for key in pretrained_state.keys():
                if key.startswith(teacher_layer_name):
                    new_key = key.replace(teacher_layer_name, student_layer_name)
                    if new_key in current_state and current_state[new_key].shape == pretrained_state[key].shape:
                        current_state[new_key] = pretrained_state[key].clone()
                        loaded_count += 1
                    else:
                        skipped_count += 1

        # 应用加载的权重
        model.model.load_state_dict(current_state)

        print(f"✅ 权重加载完成: {loaded_count}个参数已加载, {skipped_count}个参数跳过")
        print("🎯 教师和学生网络现在都使用预训练权重初始化!")

        return True

    except Exception as e:
        print(f"❌ 智能权重加载失败: {e}")
        return False

def main():
    """主训练流程"""
    print("🚀 开始COCO知识蒸馏训练...")
    print("=" * 50)
    
    # 清理CUDA缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🔧 CUDA设备: {torch.cuda.get_device_name()}")
        print(f"💾 可用显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    try:
        # 1. 加载模型 - 智能预训练权重加载
        print(f"📂 加载模型: {model_file}")
        print("🔧 智能加载: 将为教师和学生网络分别加载兼容的预训练权重")
        model = YOLO(model_file, task='detect')

        # 2. 智能加载预训练权重到教师和学生网络
        try:
            print("📥 尝试加载YOLOv8预训练权重到教师和学生网络...")
            load_pretrained_weights_intelligently(model)
        except Exception as e:
            print(f"⚠️ 预训练权重加载失败: {e}")
            print("💡 继续使用随机初始化权重进行训练")
        
        # 2. 配置训练参数 - 使用成功的配置
        train_args = {
            'data': dataset,
            'epochs': epochs,
            'batch': batch_size,
            'device': device,
            'save': bool_save,
            'save_period': save_period,
            'freeze': teacher_peer_list,  # 冻结教师层
            'resume': bool_resume,
            'amp': True,  # 启用混合精度 - 水果数据集成功配置
            'workers': 8,  # 恢复成功配置的工作进程数
            'cache': False,
            'deterministic': True,
            'project': 'runs/detect',
            'name': f'coco_distill_T{hyp_T}_ep{epochs}',
            'exist_ok': True,
            'patience': 100,
            'verbose': True,
            'plots': True,
            'val': True
        }
        
        print("⚙️ 训练参数:")
        for key, value in train_args.items():
            if key != 'freeze':
                print(f"   {key}: {value}")
        print(f"   freeze: {len(train_args['freeze'])} 层 (教师层)")
        
        # 3. 开始训练
        print("\n🎯 开始训练...")
        results = model.train(**train_args)
        
        print("✅ 训练完成!")
        print(f"📊 最终结果: {results}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        raise

if __name__ == '__main__':
    main()
